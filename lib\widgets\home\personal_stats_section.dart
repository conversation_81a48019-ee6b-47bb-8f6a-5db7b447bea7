import 'package:flutter/material.dart';
import '../../theme/app_styles.dart';

/// قسم الإحصائيات الشخصية - Personal Stats Section
///
/// يعرض إحصائيات سريعة ومهمة للمريض:
/// - عدد الجلسات المكتملة
/// - نسبة التقدم
/// - المواعيد القادمة
/// - الأهداف المحققة
class PersonalStatsSection extends StatefulWidget {
  const PersonalStatsSection({super.key});

  @override
  State<PersonalStatsSection> createState() => _PersonalStatsSectionState();
}

class _PersonalStatsSectionState extends State<PersonalStatsSection>
    with TickerProviderStateMixin {
  late AnimationController _progressAnimationController;
  late AnimationController _countAnimationController;
  late Animation<double> _progressAnimation;
  late Animation<int> _sessionsCountAnimation;
  late Animation<int> _goalsCountAnimation;

  // بيانات وهمية - ستأتي من قاعدة البيانات لاحقاً
  final int _totalSessions = 24;
  final int _completedGoals = 8;
  final double _progressPercentage = 0.75;
  final int _upcomingAppointments = 2;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _countAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: _progressPercentage)
        .animate(
          CurvedAnimation(
            parent: _progressAnimationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _sessionsCountAnimation = IntTween(begin: 0, end: _totalSessions).animate(
      CurvedAnimation(parent: _countAnimationController, curve: Curves.easeOut),
    );

    _goalsCountAnimation = IntTween(begin: 0, end: _completedGoals).animate(
      CurvedAnimation(parent: _countAnimationController, curve: Curves.easeOut),
    );

    // تشغيل الأنيميشن مع تأخير
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _progressAnimationController.forward();
        _countAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();
    _countAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // العنوان البسيط
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Row(
            children: [
              Container(
                width: 4,
                height: 24,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: AppDimensions.marginMedium),
              Text(
                'إحصائياتك الشخصية',
                style: AppTextStyles.headline4.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: AppDimensions.marginLarge),

        // الإحصائيات الرئيسية - 50% التقدم العام، الجلسات والأهداف تحت بعض
        Row(
          children: [
            // التقدم العام - 50%
            Expanded(flex: 2, child: _buildProgressCard()),

            const SizedBox(width: AppDimensions.marginMedium),

            // الجلسات والأهداف في عمود - 50%
            Expanded(
              child: Column(
                children: [
                  // الجلسات
                  _buildStatCard(
                    title: 'الجلسات',
                    animation: _sessionsCountAnimation,
                    icon: Icons.event_available_outlined,
                    color: AppColors.primary,
                  ),

                  const SizedBox(height: AppDimensions.marginMedium),

                  // الأهداف
                  _buildStatCard(
                    title: 'الأهداف',
                    animation: _goalsCountAnimation,
                    icon: Icons.flag_outlined,
                    color: AppColors.success,
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: AppDimensions.marginMedium),

        // رسم بياني للتقدم الأسبوعي
        _buildWeeklyProgressChart(),

        const SizedBox(height: AppDimensions.marginMedium),

        // المواعيد القادمة
        _buildUpcomingAppointments(),
      ],
    );
  }

  Widget _buildProgressCard() {
    return Container(
      height: 150,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'التقدم العام',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AnimatedBuilder(
                    animation: _progressAnimation,
                    builder: (context, child) {
                      return Text(
                        '${(_progressAnimation.value * 100).round()}%',
                        style: AppTextStyles.headline2.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w300,
                          height: 1,
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'مكتمل',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textSecondary,
                      letterSpacing: 1,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required Animation<int> animation,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      height: 72,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
      ),
      child: Row(
        children: [
          // Left section - Icon
          Container(
            width: 60,
            height: double.infinity,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            child: Center(
              child: Container(
                width: 6,
                height: 6,
                decoration: BoxDecoration(color: color, shape: BoxShape.circle),
              ),
            ),
          ),

          // Right section - Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AnimatedBuilder(
                    animation: animation,
                    builder: (context, child) {
                      return Text(
                        '${animation.value}',
                        style: AppTextStyles.headline4.copyWith(
                          color: color,
                          fontWeight: FontWeight.w300,
                          height: 1,
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 2),
                  Text(
                    title,
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.textSecondary,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeeklyProgressChart() {
    final List<double> weeklyData = [
      0.3,
      0.6,
      0.4,
      0.8,
      0.7,
      0.9,
      0.75,
    ]; // بيانات وهمية
    final List<String> days = ['س', 'ح', 'ن', 'ث', 'ر', 'خ', 'ج'];

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.border.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.trending_up, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'التقدم الأسبوعي',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '+12%',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // الرسم البياني البسيط
          SizedBox(
            height: 60,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: List.generate(7, (index) {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      width: 20,
                      height: weeklyData[index] * 40, // ارتفاع متغير
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      days[index],
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingAppointments() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.warning.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusSmall,
                  ),
                ),
                child: Icon(
                  Icons.schedule_outlined,
                  color: AppColors.warning,
                  size: 20,
                ),
              ),

              const SizedBox(width: AppDimensions.marginMedium),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المواعيد القادمة',
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      'لديك $_upcomingAppointments موعد قادم',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),

              Icon(
                Icons.arrow_back_ios_new,
                color: AppColors.textSecondary,
                size: 16,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
