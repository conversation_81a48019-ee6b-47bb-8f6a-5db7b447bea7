import 'package:flutter/material.dart';
import '../../theme/app_styles.dart';

/// قسم الإحصائيات الشخصية - Personal Stats Section
///
/// يعرض إحصائيات سريعة ومهمة للمريض:
/// - عدد الجلسات المكتملة
/// - نسبة التقدم
/// - المواعيد القادمة
/// - الأهداف المحققة
class PersonalStatsSection extends StatefulWidget {
  const PersonalStatsSection({super.key});

  @override
  State<PersonalStatsSection> createState() => _PersonalStatsSectionState();
}

class _PersonalStatsSectionState extends State<PersonalStatsSection>
    with TickerProviderStateMixin {
  late AnimationController _progressAnimationController;
  late AnimationController _countAnimationController;
  late Animation<double> _progressAnimation;
  late Animation<int> _sessionsCountAnimation;
  late Animation<int> _goalsCountAnimation;

  // بيانات وهمية - ستأتي من قاعدة البيانات لاحقاً
  final int _totalSessions = 24;
  final int _completedGoals = 8;
  final double _progressPercentage = 0.75;
  final int _upcomingAppointments = 2;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _countAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: _progressPercentage)
        .animate(
          CurvedAnimation(
            parent: _progressAnimationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _sessionsCountAnimation = IntTween(begin: 0, end: _totalSessions).animate(
      CurvedAnimation(parent: _countAnimationController, curve: Curves.easeOut),
    );

    _goalsCountAnimation = IntTween(begin: 0, end: _completedGoals).animate(
      CurvedAnimation(parent: _countAnimationController, curve: Curves.easeOut),
    );

    // تشغيل الأنيميشن مع تأخير
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _progressAnimationController.forward();
        _countAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();
    _countAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // العنوان
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Row(
            children: [
              Container(
                width: 4,
                height: 24,
                decoration: BoxDecoration(
                  color: AppColors.success,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: AppDimensions.marginMedium),
              Text(
                'إحصائياتك الشخصية',
                style: AppTextStyles.headline4.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: AppDimensions.marginLarge),

        // الإحصائيات الرئيسية
        Row(
          children: [
            // التقدم العام
            Expanded(flex: 2, child: _buildProgressCard()),

            const SizedBox(width: AppDimensions.marginMedium),

            // الإحصائيات الجانبية
            Expanded(
              child: Column(
                children: [
                  _buildStatCard(
                    title: 'الجلسات',
                    animation: _sessionsCountAnimation,
                    icon: Icons.event_available_outlined,
                    color: AppColors.primary,
                  ),
                  const SizedBox(height: AppDimensions.marginMedium),
                  _buildStatCard(
                    title: 'الأهداف',
                    animation: _goalsCountAnimation,
                    icon: Icons.flag_outlined,
                    color: AppColors.success,
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: AppDimensions.marginMedium),

        // المواعيد القادمة
        _buildUpcomingAppointments(),
      ],
    );
  }

  Widget _buildProgressCard() {
    return Container(
      height: 120, // تقليل الارتفاع من 160 إلى 120
      padding: const EdgeInsets.all(
        AppDimensions.paddingMedium,
      ), // تقليل الـ padding
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.trending_up_outlined,
                color: AppColors.textOnPrimary,
                size: 24,
              ),
              const SizedBox(width: AppDimensions.marginSmall),
              Text(
                'التقدم العام',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.textOnPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          const Spacer(),

          // دائرة التقدم
          Center(
            child: SizedBox(
              width: 60, // تقليل الحجم من 80 إلى 60
              height: 60,
              child: AnimatedBuilder(
                animation: _progressAnimation,
                builder: (context, child) {
                  return Stack(
                    alignment: Alignment.center,
                    children: [
                      CircularProgressIndicator(
                        value: _progressAnimation.value,
                        strokeWidth: 4, // تقليل سمك الخط
                        backgroundColor: AppColors.textOnPrimary.withValues(
                          alpha: 0.3,
                        ),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.textOnPrimary,
                        ),
                      ),
                      Text(
                        '${(_progressAnimation.value * 100).round()}%',
                        style: AppTextStyles.bodyLarge.copyWith(
                          // تصغير النص
                          color: AppColors.textOnPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),

          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required Animation<int> animation,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      height: 72,
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: color.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 6),
              Text(
                title,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          const Spacer(),

          AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              return Text(
                '${animation.value}',
                style: AppTextStyles.headline3.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingAppointments() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.warning.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
            ),
            child: Icon(
              Icons.schedule_outlined,
              color: AppColors.warning,
              size: 20,
            ),
          ),

          const SizedBox(width: AppDimensions.marginMedium),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'المواعيد القادمة',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'لديك $_upcomingAppointments موعد قادم',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          Icon(
            Icons.arrow_back_ios_new,
            color: AppColors.textSecondary,
            size: 16,
          ),
        ],
      ),
    );
  }
}
